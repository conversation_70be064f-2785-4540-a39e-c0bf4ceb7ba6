import React from 'react';
import { ToolViewProps } from './types';
import { formatTimestamp, getToolTitle } from './utils';
import { getToolIcon } from '../utils';
import {
  CircleDashed,
  CheckCircle,
  AlertTriangle,
  Mail,
  FileText,
  Github,
  Slack,
  Calendar,
  Database,
  HardDrive,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Service-specific icons
const getServiceIcon = (serviceName: string) => {
  switch (serviceName.toLowerCase()) {
    case 'gmail':
      return Mail;
    case 'notion':
      return FileText;
    case 'github':
      return Github;
    case 'slack':
      return Slack;
    case 'googledrive':
    case 'google_drive':
    case 'google-drive':
      return HardDrive;
    case 'google_calendar':
    case 'googlecalendar':
      return Calendar;
    default:
      return Database;
  }
};

// Service-specific colors
const getServiceColor = (serviceName: string) => {
  switch (serviceName.toLowerCase()) {
    case 'gmail':
      return 'text-red-600 dark:text-red-400';
    case 'notion':
      return 'text-gray-800 dark:text-gray-200';
    case 'github':
      return 'text-gray-900 dark:text-gray-100';
    case 'slack':
      return 'text-purple-600 dark:text-purple-400';
    case 'googledrive':
    case 'google_drive':
    case 'google-drive':
      return 'text-blue-600 dark:text-blue-400';
    case 'google_calendar':
    case 'googlecalendar':
      return 'text-green-600 dark:text-green-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
};

export function ComposioToolView({
  name = 'composio-tool',
  assistantContent,
  toolContent,
  isSuccess = true,
  isStreaming = false,
  assistantTimestamp,
  toolTimestamp,
}: ToolViewProps) {
  // Extract service name and action from tool name
  const { serviceName, action, displayName } = React.useMemo(() => {
    // Handle patterns like "gmail-action", "notion-action", etc.
    const match = name.match(/^([^-]+)-action$/);
    if (match) {
      return {
        serviceName: match[1],
        action: 'action',
        displayName: `${match[1].charAt(0).toUpperCase() + match[1].slice(1)} Action`
      };
    }

    // Fallback for other patterns
    return {
      serviceName: name.split('-')[0] || 'composio',
      action: name.split('-')[1] || 'action',
      displayName: getToolTitle(name)
    };
  }, [name]);

  const ServiceIcon = getServiceIcon(serviceName);
  const serviceColor = getServiceColor(serviceName);

  // Parse assistant content to extract request details
  const requestDetails = React.useMemo(() => {
    if (!assistantContent) return null;

    try {
      const parsed = JSON.parse(assistantContent);
      const content = parsed.content || assistantContent;

      // Extract XML content
      const xmlMatch = content.match(/<([^>]+)([^>]*)>([\s\S]*?)<\/[^>]+>/);
      if (xmlMatch) {
        const tagName = xmlMatch[1];
        const attributes = xmlMatch[2];
        const bodyContent = xmlMatch[3];

        // Parse attributes
        const attrs: Record<string, string> = {};
        const attrMatches = attributes.matchAll(/(\w+)="([^"]*)"/g);
        for (const match of attrMatches) {
          attrs[match[1]] = match[2];
        }

        return {
          action: attrs.action || 'unknown',
          parameters: attrs,
          content: bodyContent?.trim() || null,
          tagName
        };
      }

      return null;
    } catch (e) {
      return null;
    }
  }, [assistantContent]);

  // Parse tool response
  const responseDetails = React.useMemo(() => {
    if (!toolContent || isStreaming) return null;

    try {
      // First try to parse as JSON
      const parsed = JSON.parse(toolContent);

      // Check if it's our new structured response format
      if (parsed.success !== undefined && parsed.data !== undefined) {
        return {
          success: parsed.success,
          data: parsed.data,
          service: parsed.service,
          action: parsed.action,
          error: parsed.error,
          timestamp: parsed.timestamp
        };
      }

      // Handle legacy format
      if (parsed.content) {
        // Look for tool_result tags
        const toolResultMatch = parsed.content.match(
          /<tool_result>\s*<([^>]+)>([\s\S]*?)<\/[^>]+>\s*<\/tool_result>/
        );

        if (toolResultMatch) {
          const resultContent = toolResultMatch[2];

          // Check if it's an error message
          if (resultContent.includes('❌') || resultContent.includes('error')) {
            return {
              success: false,
              error: resultContent.trim(),
              data: null
            };
          }

          return {
            success: true,
            data: resultContent.trim(),
            error: null
          };
        }
      }

      // Fallback to raw content
      return {
        success: isSuccess,
        data: parsed,
        error: null
      };

    } catch (e) {
      // Handle plain text responses
      if (toolContent.includes('❌') || toolContent.toLowerCase().includes('error')) {
        return {
          success: false,
          error: toolContent,
          data: null
        };
      }

      return {
        success: isSuccess,
        data: toolContent,
        error: null
      };
    }
  }, [toolContent, isStreaming, isSuccess]);

  // Format parameters for display
  const formatParameters = (params: Record<string, string>) => {
    const filtered = Object.entries(params).filter(([key]) => key !== 'action');
    if (filtered.length === 0) return null;

    return filtered.map(([key, value]) => (
      <div key={key} className="flex justify-between text-xs">
        <span className="font-medium text-muted-foreground">{key}:</span>
        <span className="text-right max-w-[200px] truncate">{value}</span>
      </div>
    ));
  };

  return (
    <div className="bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="px-3 py-2 bg-zinc-50 dark:bg-zinc-800 border-b border-zinc-200 dark:border-zinc-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ServiceIcon className={cn("h-4 w-4", serviceColor)} />
            <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
              {displayName}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {isStreaming ? (
              <CircleDashed className="h-4 w-4 text-blue-500 animate-spin" />
            ) : responseDetails?.success ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-xs text-muted-foreground">
              {formatTimestamp(assistantTimestamp)}
            </span>
          </div>
        </div>
      </div>

      {/* Request Details */}
      {requestDetails && (
        <div className="px-3 py-2 border-b border-zinc-100 dark:border-zinc-800">
          <div className="text-xs font-medium text-muted-foreground mb-1">Request</div>
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="font-medium text-muted-foreground">Action:</span>
              <span className="font-mono text-blue-600 dark:text-blue-400">
                {requestDetails.action}
              </span>
            </div>
            {requestDetails.parameters && formatParameters(requestDetails.parameters)}
            {requestDetails.content && (
              <div className="mt-2">
                <div className="text-xs font-medium text-muted-foreground mb-1">Content:</div>
                <div className="text-xs bg-zinc-50 dark:bg-zinc-800 rounded p-2 border">
                  {requestDetails.content}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Response */}
      {isStreaming ? (
        <div className="px-3 py-4 flex flex-col items-center justify-center">
          <CircleDashed className="h-8 w-8 mb-3 text-blue-500 animate-spin" />
          <p className="text-base font-medium text-zinc-700 dark:text-zinc-200">
            Executing {serviceName} action...
          </p>
          <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400">
            This may take a few moments
          </p>
        </div>
      ) : responseDetails ? (
        <div className="px-3 py-2">
          <div className="flex justify-between items-center mb-1">
            <div className="text-xs font-medium text-muted-foreground">Response</div>
            <div className={cn(
              "px-2 py-0.5 rounded-full text-xs",
              responseDetails.success
                ? "bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-300"
                : "bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-300"
            )}>
              {responseDetails.success ? 'Success' : 'Failed'}
            </div>
          </div>

          <div className="rounded bg-zinc-50 dark:bg-zinc-900 p-2 border border-zinc-100 dark:border-zinc-800">
            {responseDetails.error ? (
              <div className="text-xs text-red-600 dark:text-red-400">
                {responseDetails.error}
              </div>
            ) : responseDetails.data ? (
              <div className="text-xs">
                {typeof responseDetails.data === 'string' ? (
                  <pre className="whitespace-pre-wrap font-mono">
                    {responseDetails.data}
                  </pre>
                ) : (
                  <pre className="whitespace-pre-wrap font-mono">
                    {JSON.stringify(responseDetails.data, null, 2)}
                  </pre>
                )}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground">No response data</div>
            )}
          </div>

          {toolTimestamp && (
            <div className="text-xs text-muted-foreground mt-1 text-right">
              Completed {formatTimestamp(toolTimestamp)}
            </div>
          )}
        </div>
      ) : (
        <div className="px-3 py-4 flex flex-col items-center justify-center">
          <ServiceIcon className={cn("h-8 w-8 mb-3", serviceColor)} />
          <p className="text-base font-medium text-zinc-700 dark:text-zinc-200">
            No response available
          </p>
        </div>
      )}
    </div>
  );
}
