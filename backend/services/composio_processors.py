"""
Composio Tool Processors - Parameter Mapping and Response Processing

This module implements Composio's official processor system for handling
parameter mapping and response processing at scale.

Based on Composio's documentation: https://docs.composio.dev/tool-calling/processing-tools
"""

import logging
from typing import Dict, Any, Callable, Optional, List
from composio import Action

logger = logging.getLogger(__name__)

# ============================================================================
# PARAMETER MAPPING CONFIGURATIONS
# ============================================================================

# Central mapping configuration for all services
PARAMETER_MAPPINGS = {
    "gmail": {
        "GMAIL_SEND_EMAIL": {
            "to": "recipient_email",
            "from": "sender_email", 
            "cc": "cc",
            "bcc": "bcc",
            "subject": "subject",
            "body": "body",
            "message": "body",
            "content": "body",
            "text": "body",
            "html": "body",
            "attachment": "attachment",
            "attachments": "attachment"
        },
        "GMAIL_READ_EMAIL": {
            "id": "message_id",
            "message_id": "message_id",
            "email_id": "message_id"
        },
        "GMAIL_SEARCH_EMAILS": {
            "query": "query",
            "q": "query",
            "search": "query",
            "limit": "max_results",
            "max_results": "max_results",
            "count": "max_results"
        }
    },
    "notion": {
        "NOTION_CREATE_PAGE": {
            "title": "page_title",
            "name": "page_title",
            "content": "page_content",
            "body": "page_content",
            "text": "page_content",
            "parent": "parent_id",
            "parent_id": "parent_id",
            "database": "database_id",
            "database_id": "database_id"
        },
        "NOTION_READ_PAGE": {
            "id": "page_id",
            "page_id": "page_id"
        },
        "NOTION_SEARCH": {
            "query": "query",
            "q": "query",
            "search": "query",
            "limit": "page_size",
            "max_results": "page_size",
            "count": "page_size"
        }
    }
}

# ============================================================================
# SCHEMA PROCESSORS
# ============================================================================

def create_schema_processor(service_name: str, action: str) -> Callable[[Dict], Dict]:
    """
    Create a schema processor that adds parameter aliases to the schema.
    This helps LLMs understand alternative parameter names.
    """
    mappings = PARAMETER_MAPPINGS.get(service_name, {}).get(action, {})
    
    def schema_processor(schema: Dict[str, Any]) -> Dict[str, Any]:
        """Add parameter aliases to schema descriptions"""
        try:
            params = schema.get("parameters", {}).get("properties", {})
            
            # Add aliases to parameter descriptions
            for alias, actual_param in mappings.items():
                if actual_param in params and alias != actual_param:
                    current_desc = params[actual_param].get("description", "")
                    if f"(alias: {alias})" not in current_desc:
                        params[actual_param]["description"] = f"{current_desc} (alias: {alias})"
            
            logger.debug(f"Enhanced schema for {service_name}.{action} with aliases")
            return schema
            
        except Exception as e:
            logger.warning(f"Schema processor error for {service_name}.{action}: {e}")
            return schema
    
    return schema_processor

# ============================================================================
# PARAMETER PROCESSORS (PRE-EXECUTION)
# ============================================================================

def create_parameter_mapper(service_name: str, action: str) -> Callable[[Dict], Dict]:
    """
    Create a parameter mapper that converts common parameter names 
    to Composio's expected format.
    """
    mappings = PARAMETER_MAPPINGS.get(service_name, {}).get(action, {})
    
    def parameter_mapper(inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Map common parameter names to Composio's expected format"""
        try:
            mapped_inputs = {}
            
            for key, value in inputs.items():
                # Use mapping if available, otherwise keep original key
                mapped_key = mappings.get(key, key)
                mapped_inputs[mapped_key] = value
                
                if mapped_key != key:
                    logger.debug(f"Mapped parameter {key} -> {mapped_key}")
            
            logger.info(f"Parameter mapping for {service_name}.{action}: {len(mappings)} mappings applied")
            return mapped_inputs
            
        except Exception as e:
            logger.error(f"Parameter mapping error for {service_name}.{action}: {e}")
            return inputs  # Return original inputs on error
    
    return parameter_mapper

# ============================================================================
# RESPONSE PROCESSORS (POST-EXECUTION)
# ============================================================================

def create_response_processor(service_name: str, action: str) -> Callable[[Dict], Dict]:
    """
    Create a response processor that cleans and structures Composio responses.
    """
    
    def response_processor(response: Dict[str, Any]) -> Dict[str, Any]:
        """Process and clean Composio response"""
        try:
            # Handle Composio's standard response format
            if isinstance(response, dict):
                # Extract the actual data from Composio's response wrapper
                if "data" in response:
                    actual_data = response["data"]
                    
                    # Create a clean response structure
                    processed_response = {
                        "success": response.get("successful", response.get("successfull", True)),
                        "data": actual_data,
                        "service": service_name,
                        "action": action,
                        "timestamp": response.get("timestamp"),
                        "version": response.get("version")
                    }
                    
                    # Add error information if present
                    if "error" in response and response["error"]:
                        processed_response["success"] = False
                        processed_response["error"] = response["error"]
                    
                    # Add logs if present (for debugging)
                    if "logs" in response:
                        processed_response["logs"] = response["logs"]
                    
                    logger.debug(f"Processed response for {service_name}.{action}")
                    return processed_response
                
                # If no "data" field, return the response as-is but add metadata
                else:
                    return {
                        "success": True,
                        "data": response,
                        "service": service_name,
                        "action": action
                    }
            
            # If response is not a dict, wrap it
            return {
                "success": True,
                "data": response,
                "service": service_name,
                "action": action
            }
            
        except Exception as e:
            logger.error(f"Response processing error for {service_name}.{action}: {e}")
            return {
                "success": False,
                "error": f"Response processing failed: {str(e)}",
                "raw_response": response,
                "service": service_name,
                "action": action
            }
    
    return response_processor

# ============================================================================
# PROCESSOR FACTORY
# ============================================================================

def create_processors_for_service(service_name: str, actions: List[str]) -> Dict[str, Dict[str, Callable]]:
    """
    Create all processors (schema, pre, post) for a service and its actions.
    
    Returns a dictionary in the format expected by Composio's get_tools() method:
    {
        "schema": {Action.GMAIL_SEND_EMAIL: schema_processor_func, ...},
        "pre": {Action.GMAIL_SEND_EMAIL: parameter_mapper_func, ...},
        "post": {Action.GMAIL_SEND_EMAIL: response_processor_func, ...}
    }
    """
    processors = {
        "schema": {},
        "pre": {},
        "post": {}
    }
    
    for action in actions:
        # Create processors for this action
        schema_proc = create_schema_processor(service_name, action)
        param_mapper = create_parameter_mapper(service_name, action)
        response_proc = create_response_processor(service_name, action)
        
        # Try to get the Action enum
        try:
            if hasattr(Action, action):
                action_enum = getattr(Action, action)
            else:
                # Try common variations
                action_variations = [
                    f"{service_name.upper()}_{action}",
                    f"{service_name.upper()}_{action.upper()}",
                    action.upper()
                ]
                
                action_enum = None
                for variation in action_variations:
                    if hasattr(Action, variation):
                        action_enum = getattr(Action, variation)
                        break
                
                if action_enum is None:
                    logger.warning(f"Could not find Action enum for {action}")
                    continue
            
            # Add processors for this action
            processors["schema"][action_enum] = schema_proc
            processors["pre"][action_enum] = param_mapper
            processors["post"][action_enum] = response_proc
            
            logger.debug(f"Created processors for {service_name}.{action}")
            
        except Exception as e:
            logger.error(f"Failed to create processors for {service_name}.{action}: {e}")
            continue
    
    return processors

# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================

def get_gmail_processors() -> Dict[str, Dict[str, Callable]]:
    """Get all processors for Gmail actions"""
    gmail_actions = ["GMAIL_SEND_EMAIL", "GMAIL_READ_EMAIL", "GMAIL_SEARCH_EMAILS"]
    return create_processors_for_service("gmail", gmail_actions)

def get_notion_processors() -> Dict[str, Dict[str, Callable]]:
    """Get all processors for Notion actions"""
    notion_actions = ["NOTION_CREATE_PAGE", "NOTION_READ_PAGE", "NOTION_SEARCH"]
    return create_processors_for_service("notion", notion_actions)

def get_all_processors() -> Dict[str, Dict[str, Callable]]:
    """Get processors for all supported services"""
    gmail_procs = get_gmail_processors()
    notion_procs = get_notion_processors()
    
    # Merge all processors
    all_processors = {
        "schema": {},
        "pre": {},
        "post": {}
    }
    
    for proc_type in ["schema", "pre", "post"]:
        all_processors[proc_type].update(gmail_procs[proc_type])
        all_processors[proc_type].update(notion_procs[proc_type])
    
    return all_processors
