"""
Enhanced Composio Authentication Service

This service implements the Composio entity-based authentication pattern for per-user
MCP connections, following the official Composio documentation patterns.

Key Features:
- Entity-based user isolation (user_id as entity_id)
- OAuth flow management with proper state handling
- Connection lifecycle management
- MCP server URL generation and management
- Robust error handling and validation
"""

import os
import json
import secrets
import asyncio
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta, timezone
import requests
from supabase import create_client, Client
from pydantic import BaseModel, Field
from composio import ComposioToolSet, App, Action
from uuid import uuid4
import logging

# Import our integration configuration
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.composio_integrations import (
    get_integration,
    get_all_integrations,
    ComposioIntegration,
)

logger = logging.getLogger(__name__)


# Enhanced Models
class ConnectionRequest(BaseModel):
    redirect_url: str
    state: str
    connection_id: Optional[str] = None
    connected_account_id: Optional[str] = None
    entity_id: str


class UserConnection(BaseModel):
    id: str
    user_id: str
    service_name: str
    composio_connection_id: str
    composio_entity_id: str
    mcp_server_id: Optional[str] = None
    mcp_url: Optional[str] = None
    status: str
    auth_config_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    connected_account_id: Optional[str] = None
    expires_at: Optional[datetime] = None
    refresh_token: Optional[str] = None
    last_verified_at: Optional[datetime] = None
    is_active: Optional[bool] = None
    error_message: Optional[str] = None


class ConnectionStatus(BaseModel):
    status: str
    connected: bool
    service_name: str
    mcp_url: Optional[str] = None
    connection_id: Optional[str] = None
    error: Optional[str] = None


class ServiceIntegration(BaseModel):
    service_name: str
    display_name: str
    description: str
    category: str
    icon_url: Optional[str] = None
    auth_type: str = "oauth2"
    available: bool = True


class ComposioAuthService:
    """Enhanced Composio Authentication Service with entity-based user isolation."""

    def __init__(
        self,
        composio_api_key: str,
        supabase_url: str,
        supabase_key: str,
    ):
        self.api_key = composio_api_key
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key

        # Initialize Composio toolset
        self.toolset = ComposioToolSet(api_key=composio_api_key)

        # Supabase client for database operations
        self.supabase: Client = create_client(supabase_url, supabase_key)

        # API configuration
        self.base_url = "https://backend.composio.dev"
        self.headers = {
            "X-API-Key": composio_api_key,
            "Content-Type": "application/json",
        }

        # In-memory state storage for OAuth flows (consider Redis for production)
        self._state_storage: Dict[str, Dict[str, Any]] = {}

    @classmethod
    def from_env(cls):
        import os

        return cls(
            composio_api_key=os.getenv("COMPOSIO_API_KEY"),
            supabase_url=os.getenv("SUPABASE_URL"),
            supabase_key=os.getenv("SUPABASE_SERVICE_ROLE_KEY"),
        )

    async def initiate_connection(
        self, user_id: str, service_name: str
    ) -> ConnectionRequest:
        """
        Initiate OAuth connection using Composio's documented entity-based approach.

        This follows the Composio pattern:
        1. Get or create entity for user
        2. Initiate connection for the entity (Composio handles OAuth internally)
        3. Return connection request for polling with wait_until_active
        """
        try:
            logger.info(
                f"🔐 Initiating connection for user {user_id}, service {service_name}"
            )

            # Get integration configuration
            integration = get_integration(service_name)
            if not integration:
                raise ValueError(f"No integration found for service: {service_name}")

            # Generate secure state for tracking (for our internal use)
            state = str(uuid4())

            # Get or create entity for this user (user_id serves as entity_id)
            entity = self.toolset.get_entity(id=user_id)
            logger.info(f"✅ Entity retrieved/created: {entity.id}")

            # Store pending connection state for tracking
            await self._store_pending_connection(
                user_id=user_id,
                service_name=service_name,
                state=state,
                integration_id=integration.integration_id,
            )

            try:
                # Use Composio's documented approach - let Composio handle OAuth internally
                logger.info(
                    f"🔄 Initiating connection using Composio's internal OAuth handling..."
                )

                # For Google services, use the app name format that Composio expects
                app_name = service_name.upper()
                if service_name == "google_drive":
                    app_name = "GOOGLEDRIVE"  # Composio uses this format for Google Drive
                elif service_name == "google_calendar":
                    app_name = "GOOGLECALENDAR"
                elif service_name == "google_docs":
                    app_name = "GOOGLEDOCS"
                elif service_name == "google_sheets":
                    app_name = "GOOGLESHEETS"

                connection_request = entity.initiate_connection(
                    app_name=app_name,
                    # No custom redirect_url - let Composio handle OAuth flow internally
                    labels=[f"atlas-user-{user_id}", f"service-{service_name}"],
                )

                # Extract redirect URL from response
                if hasattr(connection_request, "redirectUrl"):
                    redirect_url = connection_request.redirectUrl
                    connected_account_id = getattr(
                        connection_request, "connectedAccountId", None
                    )
                elif isinstance(connection_request, dict):
                    redirect_url = connection_request.get(
                        "redirectUrl"
                    ) or connection_request.get("redirect_url")
                    connected_account_id = connection_request.get(
                        "connectedAccountId"
                    ) or connection_request.get("connected_account_id")
                else:
                    raise ValueError("Invalid response format from Composio")

                if not redirect_url:
                    raise ValueError("No redirect URL received from Composio")

                # Update state storage with connection details
                self._state_storage[state].update(
                    {
                        "connected_account_id": connected_account_id,
                        "redirect_url": redirect_url,
                        "connection_request": connection_request,  # Store for wait_until_active
                    }
                )

                logger.info(f"✅ Connection initiated successfully: {redirect_url}")

                return ConnectionRequest(
                    redirect_url=redirect_url,
                    state=state,
                    connected_account_id=connected_account_id,
                    entity_id=user_id,
                )

            except Exception as composio_error:
                logger.error(f"⚠️ Composio SDK error: {composio_error}")
                # Fallback to direct API approach if SDK fails
                return await self._initiate_connection_fallback(
                    user_id, service_name, integration, state
                )

        except Exception as e:
            logger.error(f"❌ Failed to initiate connection: {e}")
            raise

    async def _initiate_connection_fallback(
        self,
        user_id: str,
        service_name: str,
        integration: ComposioIntegration,
        state: str,
    ) -> ConnectionRequest:
        """Fallback method using direct API calls."""
        logger.info("🔄 Using fallback API approach...")

        callback_url = f"{os.getenv('BACKEND_URL', 'http://localhost:8000')}/api/v1/connections/oauth/callback"

        # Use v3 API format as suggested by the error
        response = requests.post(
            f"{self.base_url}/api/v3/connectedAccounts",
            headers=self.headers,
            json={
                "data": {
                    "integrationId": integration.integration_id,
                    "entityId": user_id,
                    "redirectUri": callback_url,
                    "labels": [f"atlas-user-{user_id}", f"service-{service_name}"],
                }
            },
        )

        if not response.ok:
            error_text = response.text
            logger.error(f"❌ API Error: {response.status_code} - {error_text}")
            raise Exception(
                f"Failed to initiate connection: {response.status_code} - {error_text}"
            )

        connection_data = response.json()
        redirect_url = connection_data.get("redirectUrl") or connection_data.get(
            "redirect_url"
        )
        connected_account_id = connection_data.get(
            "connectedAccountId"
        ) or connection_data.get("id")

        if not redirect_url:
            raise ValueError("No redirect URL received from Composio API")

        # Update state storage
        self._state_storage[state].update(
            {"connected_account_id": connected_account_id, "redirect_url": redirect_url}
        )

        return ConnectionRequest(
            redirect_url=redirect_url,
            state=state,
            connected_account_id=connected_account_id,
            entity_id=user_id,
        )

    async def wait_for_connection_activation(
        self, state: str, timeout: int = 120
    ) -> UserConnection:
        """
        Wait for connection activation using Composio's documented wait_until_active pattern.

        This follows the Composio documentation pattern:
        1. Use the connection_request from initiate_connection
        2. Call wait_until_active to poll until connection is ACTIVE
        3. Generate MCP URL and store connection details
        """
        try:
            logger.info(f"🔄 Waiting for connection activation for state: {state}")

            # Validate state
            if state not in self._state_storage:
                raise ValueError(f"Invalid or expired state: {state}")

            state_data = self._state_storage[state]
            user_id = state_data["user_id"]
            service_name = state_data["service_name"]
            integration_id = state_data["integration_id"]
            connection_request = state_data.get("connection_request")

            if not connection_request:
                raise ValueError("No connection request found for state")

            logger.info(
                f"✅ State validated for user {user_id}, service {service_name}"
            )

            # Use Composio's documented wait_until_active pattern
            logger.info(f"⏳ Using wait_until_active pattern (timeout: {timeout}s)...")
            try:
                active_connection = connection_request.wait_until_active(
                    client=self.toolset.client, timeout=timeout
                )
                logger.info(f"✅ Connection activated: {active_connection.id}")

            except Exception as wait_error:
                logger.error(f"❌ wait_until_active failed: {wait_error}")
                raise TimeoutError(
                    f"Connection did not become active within {timeout} seconds: {wait_error}"
                )

            # Generate MCP URL
            mcp_url = await self._generate_mcp_url(
                user_id, service_name, active_connection.id
            )

            # Store connection in database
            connection = await self._store_active_connection(
                user_id=user_id,
                service_name=service_name,
                composio_connection_id=active_connection.id,
                mcp_url=mcp_url,
                integration_id=integration_id,
            )

            # Clean up state
            del self._state_storage[state]

            logger.info(f"🎉 Connection completed successfully: {connection.id}")
            return connection

        except Exception as e:
            logger.error(f"❌ Connection activation failed: {e}")
            # Clean up state on error
            if state in self._state_storage:
                del self._state_storage[state]
            raise

    async def handle_oauth_callback(self, code: str, state: str) -> UserConnection:
        """
        Handle OAuth callback and finalize connection.

        This follows the Composio pattern:
        1. Validate state parameter
        2. Wait for connection to become active
        3. Generate MCP URL for the connection
        4. Store connection details in database
        """
        try:
            logger.info(f"🔄 Handling OAuth callback for state: {state}")

            # Validate state
            if state not in self._state_storage:
                raise ValueError(f"Invalid or expired state: {state}")

            state_data = self._state_storage[state]
            user_id = state_data["user_id"]
            service_name = state_data["service_name"]
            integration_id = state_data["integration_id"]
            connected_account_id = state_data.get("connected_account_id")

            logger.info(
                f"✅ State validated for user {user_id}, service {service_name}"
            )

            # Wait for connection to become active
            entity = self.toolset.get_entity(id=user_id)

            # Poll for connection status (Composio pattern)
            max_attempts = 30  # 30 seconds timeout
            for attempt in range(max_attempts):
                try:
                    connected_accounts = entity.get_connected_accounts()

                    # Find the connection that was just created
                    active_connection = None
                    for account in connected_accounts:
                        if (
                            connected_account_id and account.id == connected_account_id
                        ) or (
                            account.appName.lower() == service_name.lower()
                            and account.status == "ACTIVE"
                        ):
                            active_connection = account
                            break

                    if active_connection and active_connection.status == "ACTIVE":
                        logger.info(f"✅ Connection activated: {active_connection.id}")
                        break

                except Exception as poll_error:
                    logger.warning(
                        f"Polling attempt {attempt + 1} failed: {poll_error}"
                    )

                if attempt < max_attempts - 1:
                    await asyncio.sleep(1)  # Wait 1 second before retry
            else:
                raise TimeoutError(
                    "Connection did not become active within timeout period"
                )

            # Generate MCP URL
            mcp_url = await self._generate_mcp_url(
                user_id, service_name, active_connection.id
            )

            # Store connection in database
            connection = await self._store_active_connection(
                user_id=user_id,
                service_name=service_name,
                composio_connection_id=active_connection.id,
                mcp_url=mcp_url,
                integration_id=integration_id,
            )

            # Clean up state
            del self._state_storage[state]

            logger.info(f"🎉 Connection completed successfully: {connection.id}")
            return connection

        except Exception as e:
            logger.error(f"❌ OAuth callback failed: {e}")
            # Clean up state on error
            if state in self._state_storage:
                del self._state_storage[state]
            raise

    async def _generate_mcp_url(
        self, user_id: str, service_name: str, connection_id: str
    ) -> str:
        """Generate MCP URL following Composio's MCP server pattern."""
        # Composio's MCP URL format with user context
        base_mcp_url = "https://mcp.composio.dev"

        # Add user-specific parameters
        mcp_url = f"{base_mcp_url}?user_id={user_id}&entity_id={user_id}&connection_id={connection_id}&include_composio_helper_actions=true"

        logger.info(f"📡 Generated MCP URL: {mcp_url}")
        return mcp_url

    async def get_user_connections(self, user_id: str) -> List[UserConnection]:
        """Get all connections for a user."""
        try:
            response = (
                self.supabase.table("user_mcp_connections")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )

            return [UserConnection(**conn) for conn in response.data]
        except Exception as e:
            logger.error(f"Failed to get user connections: {e}")
            raise

    async def get_connection_by_service(
        self, user_id: str, service_name: str
    ) -> Optional[UserConnection]:
        """Get connection for a specific service with alias support."""
        try:
            # Use alias support to get canonical service name
            integration = get_integration(service_name)
            canonical_service_name = (
                integration.service_name if integration else service_name
            )

            response = (
                self.supabase.table("user_mcp_connections")
                .select("*")
                .eq("user_id", user_id)
                .eq("service_name", canonical_service_name)
                .eq("status", "active")
                .single()
                .execute()
            )

            return UserConnection(**response.data) if response.data else None
        except Exception as e:
            logger.warning(f"No active connection found for {service_name}: {e}")
            return None

    async def update_connection_verification(
        self, user_id: str, connection_id: str
    ) -> bool:
        """
        Update the last_verified_at timestamp for a connection.

        Args:
            user_id: User ID
            connection_id: Composio connection ID

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(
                f"Updating verification timestamp for connection {connection_id}"
            )

            # Update the last_verified_at timestamp with timezone-aware datetimes
            result = (
                await self.supabase.table("user_mcp_connections")
                .update(
                    {
                        "last_verified_at": datetime.now(timezone.utc).isoformat(),
                        "updated_at": datetime.now(timezone.utc).isoformat(),
                    }
                )
                .eq("composio_connection_id", connection_id)
                .eq("user_id", user_id)
                .execute()
            )

            if len(result.data) > 0:
                logger.info(
                    f"✅ Verification timestamp updated for connection {connection_id}"
                )
                return True
            else:
                logger.warning(
                    f"⚠️ No connection found with ID {connection_id} for user {user_id}"
                )
                return False

        except Exception as e:
            logger.error(f"❌ Failed to update verification timestamp: {e}")
            return False

    async def update_connection_status(
        self, user_id: str, connection_id: str, status: str
    ) -> bool:
        """
        Update the status of a connection.

        Args:
            user_id: User ID
            connection_id: Composio connection ID
            status: New status (active, error, disconnected, etc.)

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Updating status to {status} for connection {connection_id}")

            # Determine if connection is active based on status
            is_active = status.lower() == "active"

            # Update the status and is_active flag with timezone-aware datetimes
            result = (
                await self.supabase.table("user_mcp_connections")
                .update(
                    {
                        "status": status,
                        "is_active": is_active,
                        "updated_at": datetime.now(timezone.utc).isoformat(),
                        "last_verified_at": datetime.now(timezone.utc).isoformat(),
                    }
                )
                .eq("composio_connection_id", connection_id)
                .eq("user_id", user_id)
                .execute()
            )

            if len(result.data) > 0:
                logger.info(
                    f"✅ Status updated to {status} for connection {connection_id}"
                )
                return True
            else:
                logger.warning(
                    f"⚠️ No connection found with ID {connection_id} for user {user_id}"
                )
                return False

        except Exception as e:
            logger.error(f"❌ Failed to update connection status: {e}")
            return False

    async def refresh_connection(
        self, user_id: str, service_name: str, connection_id: str, refresh_token: str
    ) -> bool:
        """
        Refresh an expired connection token.

        Args:
            user_id: User ID
            service_name: Service name
            connection_id: Composio connection ID
            refresh_token: Refresh token

        Returns:
            True if refresh successful, False otherwise
        """
        try:
            logger.info(
                f"Refreshing token for {service_name} connection {connection_id}"
            )

            # Get integration configuration
            integration = get_integration(service_name)
            if not integration:
                logger.error(f"❌ No integration found for service: {service_name}")
                return False

            # Call Composio API to refresh token
            response = requests.post(
                f"{self.base_url}/api/v1/connectedAccounts/{connection_id}/refresh",
                headers=self.headers,
                json={"refreshToken": refresh_token},
            )

            if not response.ok:
                logger.error(
                    f"❌ Failed to refresh token: {response.status_code} - {response.text}"
                )
                return False

            # Parse response
            token_data = response.json()

            # Extract new tokens and expiration
            new_access_token = token_data.get("accessToken")
            new_refresh_token = token_data.get("refreshToken")
            expires_in = token_data.get("expiresIn")

            if not new_access_token:
                logger.error("❌ No access token in refresh response")
                return False

            # Calculate expiration time with timezone-aware datetime
            expires_at = (
                datetime.now(timezone.utc) + timedelta(seconds=expires_in)
                if expires_in
                else None
            )

            # Update connection in database with timezone-aware datetimes
            update_data = {
                "status": "active",
                "is_active": True,
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "last_verified_at": datetime.now(timezone.utc).isoformat(),
                "error_message": None,
            }

            # Add optional fields if present
            if expires_at:
                update_data["expires_at"] = expires_at.isoformat()

            if new_refresh_token:
                update_data["refresh_token"] = new_refresh_token

            # Update in database
            result = (
                await self.supabase.table("user_mcp_connections")
                .update(update_data)
                .eq("composio_connection_id", connection_id)
                .eq("user_id", user_id)
                .execute()
            )

            if len(result.data) > 0:
                logger.info(
                    f"✅ Token refreshed successfully for connection {connection_id}"
                )
                return True
            else:
                logger.warning(
                    f"⚠️ No connection found with ID {connection_id} for user {user_id}"
                )
                return False

        except Exception as e:
            logger.error(f"❌ Failed to refresh token: {e}")
            return False

    async def check_connection_status(
        self, user_id: str, service_name: str
    ) -> ConnectionStatus:
        """
        Check and update connection status following Composio patterns.

        This method follows the Composio documentation pattern for connection verification:
        1. Check local database status first
        2. Verify with Composio if connection exists and is ACTIVE
        3. Update local status if discrepancies found
        4. Return comprehensive status information
        """
        try:
            # Step 1: Check database first (local state)
            connection = await self.get_connection_by_service(user_id, service_name)

            if not connection:
                return ConnectionStatus(
                    status="not_connected", connected=False, service_name=service_name
                )

            # Step 2: Verify with Composio if connection exists (following entity pattern)
            if connection.status == "active":
                try:
                    # Use entity-based approach as per Composio docs
                    entity = self.toolset.get_entity(id=user_id)
                    connected_accounts = entity.get_connected_accounts()

                    # Step 3: Verify connection still exists and is ACTIVE in Composio
                    composio_connection = None
                    for account in connected_accounts:
                        if account.id == connection.composio_connection_id:
                            composio_connection = account
                            break

                    if composio_connection and composio_connection.status == "ACTIVE":
                        # Connection is valid and active in both systems
                        logger.info(
                            f"✅ Connection verified as ACTIVE for {service_name}"
                        )
                        return ConnectionStatus(
                            status="active",
                            connected=True,
                            service_name=service_name,
                            mcp_url=connection.mcp_url,
                            connection_id=connection.id,
                        )
                    else:
                        # Connection no longer active in Composio, update local status
                        logger.warning(
                            f"⚠️ Connection {connection.id} no longer ACTIVE in Composio"
                        )
                        await self._update_connection_status(connection.id, "error")

                        return ConnectionStatus(
                            status="error",
                            connected=False,
                            service_name=service_name,
                            error="Connection no longer active in Composio - please reconnect",
                        )

                except Exception as verify_error:
                    # Handle Composio API errors gracefully
                    logger.warning(
                        f"Failed to verify connection with Composio: {verify_error}"
                    )

                    # Return local status but note verification failed
                    return ConnectionStatus(
                        status=connection.status,
                        connected=connection.status == "active",
                        service_name=service_name,
                        mcp_url=connection.mcp_url,
                        connection_id=connection.id,
                        error="Could not verify with Composio - using local status",
                    )

            # Step 4: Return local status for non-active connections
            return ConnectionStatus(
                status=connection.status,
                connected=connection.status == "active",
                service_name=service_name,
                mcp_url=connection.mcp_url,
                connection_id=connection.id,
            )

        except Exception as e:
            logger.error(f"Failed to check connection status: {e}")
            return ConnectionStatus(
                status="error",
                connected=False,
                service_name=service_name,
                error=f"Status check failed: {str(e)}",
            )

    async def disconnect_service(self, user_id: str, connection_id: str) -> bool:
        """Disconnect a service for a user."""
        try:
            # Get connection details
            response = (
                self.supabase.table("user_mcp_connections")
                .select("*")
                .eq("id", connection_id)
                .eq("user_id", user_id)
                .single()
                .execute()
            )

            if not response.data:
                return False

            connection_data = response.data

            # Try to disconnect from Composio
            try:
                entity = self.toolset.get_entity(id=user_id)
                # Note: Composio SDK may not have direct disconnect method
                # This would need to be implemented based on available SDK methods
                logger.info(
                    f"Disconnecting from Composio: {connection_data['composio_connection_id']}"
                )
            except Exception as composio_error:
                logger.warning(f"Failed to disconnect from Composio: {composio_error}")

            # Update status in database
            self.supabase.table("user_mcp_connections").update(
                {"status": "disconnected", "updated_at": datetime.utcnow().isoformat()}
            ).eq("id", connection_id).execute()

            logger.info(f"✅ Service disconnected: {connection_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to disconnect service: {e}")
            return False

    async def get_available_integrations(self) -> List[ServiceIntegration]:
        """Get all available service integrations."""
        try:
            integrations = get_all_integrations()
            return [
                ServiceIntegration(
                    service_name=integration.service_name,
                    display_name=integration.display_name,
                    description=integration.description,
                    category=integration.category,
                    icon_url=integration.icon_url,
                    auth_type=integration.auth_type,
                    available=True,
                )
                for integration in integrations
            ]
        except Exception as e:
            logger.error(f"Failed to get available integrations: {e}")
            raise

    async def cleanup_expired_oauth_states(self):
        """Clean up expired OAuth states (should be called periodically)."""
        try:
            current_time = datetime.utcnow()
            expired_states = []

            for state, data in self._state_storage.items():
                if current_time > data.get("expires_at", current_time):
                    expired_states.append(state)

            for state in expired_states:
                del self._state_storage[state]

            logger.info(f"🧹 Cleaned up {len(expired_states)} expired OAuth states")

        except Exception as e:
            logger.error(f"Failed to cleanup expired states: {e}")

    # Private helper methods
    async def _store_pending_connection(
        self,
        user_id: str,
        service_name: str,
        state: str,
        integration_id: str,
        connected_account_id: Optional[str] = None,
    ):
        """Store pending connection state."""
        try:
            # Store in memory for OAuth flow
            self._state_storage[state] = {
                "user_id": user_id,
                "service_name": service_name,
                "integration_id": integration_id,
                "connected_account_id": connected_account_id,
                "expires_at": datetime.utcnow() + timedelta(minutes=10),
            }

            # Store in database
            connection_data = {
                "user_id": user_id,
                "service_name": service_name,
                "composio_connection_id": connected_account_id or f"pending_{state}",
                "composio_entity_id": user_id,
                "status": "pending_auth",
                "auth_config_id": integration_id,
            }

            # Upsert to handle existing pending connections
            self.supabase.table("user_mcp_connections").upsert(
                connection_data, on_conflict="user_id,service_name"
            ).execute()

        except Exception as e:
            logger.error(f"Failed to store pending connection: {e}")
            raise

    async def _store_active_connection(
        self,
        user_id: str,
        service_name: str,
        composio_connection_id: str,
        mcp_url: str,
        integration_id: str,
        connected_account_id: Optional[str] = None,
        refresh_token: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ) -> UserConnection:
        """Store active connection in database."""
        try:
            # Store in database
            connection_data = {
                "user_id": user_id,
                "service_name": service_name,
                "composio_connection_id": composio_connection_id,
                "composio_entity_id": user_id,  # Use user_id as entity_id
                "status": "active",
                "is_active": True,
                "mcp_url": mcp_url,
                "auth_config_id": integration_id,
                "connected_account_id": connected_account_id,
                "refresh_token": refresh_token,
                "last_verified_at": datetime.now().isoformat(),
            }

            # Add expiration if available
            if expires_at:
                connection_data["expires_at"] = expires_at.isoformat()
            connection_data["updated_at"] = datetime.utcnow().isoformat()

            response = (
                self.supabase.table("user_mcp_connections")
                .upsert(connection_data, on_conflict="user_id,service_name")
                .execute()
            )

            if not response.data:
                raise ValueError("Failed to store connection")

            return UserConnection(**response.data[0])

        except Exception as e:
            logger.error(f"Failed to store active connection: {e}")
            raise

    async def _update_connection_status(self, connection_id: str, status: str):
        """Update connection status."""
        try:
            self.supabase.table("user_mcp_connections").update(
                {"status": status, "updated_at": datetime.utcnow().isoformat()}
            ).eq("id", connection_id).execute()
        except Exception as e:
            logger.error(f"Failed to update connection status: {e}")
