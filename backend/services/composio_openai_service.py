"""
Composio XML Service - Integration with XML tool calling

This module provides XML tool calling integration with Composio,
allowing dynamic fetching and execution of tool definitions with per-user
credential management using XML wrappers around Composio's OpenAI integration.
"""

from composio_openai import ComposioToolSet, Action, App
from typing import Dict, List, Any, Optional, Union, Tuple
import logging
import os
import json
import asyncio
from datetime import datetime, timezone
from supabase import create_client, Client

# Import our existing auth service for connection management
from .composio_auth_service import ComposioAuthService

# Import our new processor system
from .composio_processors import (
    get_all_processors,
    create_parameter_mapper,
    create_response_processor,
    PARAMETER_MAPPINGS,
)

logger = logging.getLogger(__name__)


class ComposioXMLService:
    """
    Service for handling Composio's XML tool integration with per-user credentials

    This service provides methods for dynamically generating XML tool wrappers
    around Composio's OpenAI integration, enabling seamless XML-based tool calling
    with user-specific entity IDs and connection management.

    Key Features:
    - Dynamic XML tool generation based on user's active Composio connections
    - Per-user entity isolation using Composio's entity-based authentication
    - XML schema generation from Composio's OpenAPI schemas
    - Graceful error handling and connection validation
    - Integration with existing ComposioAuthService
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        supabase_url: Optional[str] = None,
        supabase_key: Optional[str] = None,
    ):
        """
        Initialize the Composio XML service.

        Args:
            api_key: Optional API key for Composio. If not provided,
                    uses the COMPOSIO_API_KEY environment variable.
            supabase_url: Optional Supabase URL. If not provided,
                         uses the SUPABASE_URL environment variable.
            supabase_key: Optional Supabase service role key. If not provided,
                         uses the SUPABASE_SERVICE_ROLE_KEY environment variable.
        """
        self.api_key = api_key or os.getenv("COMPOSIO_API_KEY")
        if not self.api_key:
            raise ValueError("COMPOSIO_API_KEY environment variable is required")

        # Initialize Supabase client for credential retrieval
        self.supabase_url = supabase_url or os.getenv("SUPABASE_URL")
        self.supabase_key = supabase_key or os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        if not self.supabase_url or not self.supabase_key:
            raise ValueError(
                "SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required"
            )

        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)

        # Initialize auth service for connection management
        self.auth_service = ComposioAuthService(
            composio_api_key=self.api_key,
            supabase_url=self.supabase_url,
            supabase_key=self.supabase_key,
        )

        # Base toolset for schema inspection (no entity_id)
        self.base_toolset = ComposioToolSet(api_key=self.api_key)

        # Cache for tool definitions and schemas
        self._tools_cache = {}
        self._schema_cache = {}
        self._user_toolsets = {}  # Cache user-specific toolsets

        # Initialize processor system for parameter mapping and response processing
        self.processors = get_all_processors()
        logger.info(
            f"Initialized Composio XML service with processors for {len(self.processors['pre'])} actions"
        )

    async def _get_user_toolset(self, user_id: str) -> Optional[ComposioToolSet]:
        """
        Get or create a user-specific ComposioToolSet with entity isolation.

        Following Composio's documented entity-based pattern:
        - Uses user_id as entity_id for perfect user isolation
        - Caches toolsets to avoid repeated initialization

        Args:
            user_id: The user's ID (used as entity_id)

        Returns:
            ComposioToolSet instance for the user, None if user has no connections
        """
        logger.debug(f"🔍 _get_user_toolset called for user: {user_id}")

        # Check cache first
        if user_id in self._user_toolsets:
            logger.debug(f"✅ Found cached toolset for user {user_id}")
            return self._user_toolsets[user_id]

        try:
            logger.debug(f"🔄 Creating new toolset for user {user_id}")

            # Check if user has any active connections
            logger.debug(f"📋 Checking active connections for user {user_id}")
            active_connections = await self.auth_service.get_user_connections(user_id)

            if not active_connections:
                logger.warning(f"⚠️ No connections found in database for user {user_id}")
                return None

            active_connections = [
                conn for conn in active_connections if conn.status == "active"
            ]

            if not active_connections:
                logger.warning(
                    f"⚠️ No active connections found for user {user_id} (found {len(active_connections)} total connections)"
                )
                return None

            logger.info(
                f"✅ Found {len(active_connections)} active connections for user {user_id}"
            )
            for conn in active_connections:
                logger.debug(f"  - {conn.service_name}: {conn.composio_connection_id}")

            # Validate API key before creating toolset
            if not self.api_key:
                logger.error(f"❌ No Composio API key available for user {user_id}")
                return None

            # Create user-specific toolset with entity_id = user_id (Composio pattern)
            logger.debug(f"🔧 Creating ComposioToolSet with entity_id={user_id}")
            user_toolset = ComposioToolSet(
                api_key=self.api_key,
                entity_id=user_id,  # This is the key for per-user isolation
            )

            # Cache the toolset
            self._user_toolsets[user_id] = user_toolset
            logger.info(
                f"🎉 Successfully created and cached toolset for user {user_id} with {len(active_connections)} connections"
            )

            return user_toolset

        except Exception as e:
            logger.error(
                f"❌ Error creating toolset for user {user_id}: {e}", exc_info=True
            )
            return None

    async def get_user_active_services(self, user_id: str) -> List[str]:
        """
        Get the list of active services for a user.

        Uses the existing ComposioAuthService to get user connections,
        ensuring consistency with the authentication system.

        Args:
            user_id: The user's ID

        Returns:
            List of active service names
        """
        try:
            # Use auth service to get connections (maintains consistency)
            connections = await self.auth_service.get_user_connections(user_id)

            # Filter for active connections only
            active_services = [
                conn.service_name
                for conn in connections
                if conn.status == "active" and conn.is_active
            ]

            logger.info(
                f"Found {len(active_services)} active services for user {user_id}: {active_services}"
            )
            return active_services

        except Exception as e:
            logger.error(f"Error fetching active services for user {user_id}: {e}")
            return []

    async def get_user_available_tools(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get available XML tool information for a user based on their active connections.

        This method returns metadata about available tools that can be used to generate
        XML tool instances. It follows Composio's documented patterns for tool discovery.

        Args:
            user_id: User's ID

        Returns:
            List of tool metadata dictionaries containing:
            - service_name: Name of the service (e.g., "gmail", "notion")
            - app_enum: Composio App enum value (e.g., App.GMAIL)
            - available_actions: List of available action names
            - connection_id: Composio connection ID for this service
        """
        try:
            # Get user's active services
            active_services = await self.get_user_active_services(user_id)
            if not active_services:
                logger.info(f"No active services for user {user_id}")
                return []

            # Get user's toolset
            user_toolset = await self._get_user_toolset(user_id)
            if not user_toolset:
                logger.warning(f"Could not create toolset for user {user_id}")
                return []

            available_tools = []

            for service_name in active_services:
                try:
                    # Map service name to Composio App enum
                    app_enum = self._get_app_enum(service_name)
                    if not app_enum:
                        logger.warning(f"Unknown service: {service_name}")
                        continue

                    # Get available tools for this service using Composio's documented pattern
                    logger.info(f"Fetching tools for {service_name} (user: {user_id})")

                    # Get specific actions for this service to avoid Composio's production filtering
                    specific_actions = self._get_service_actions(service_name)
                    if specific_actions:
                        logger.debug(
                            f"Requesting {len(specific_actions)} specific actions for {service_name}"
                        )
                        service_tools = user_toolset.get_tools(actions=specific_actions)
                    else:
                        # Fallback to app-based fetching
                        service_tools = user_toolset.get_tools(apps=[app_enum])

                    if service_tools:
                        # Extract action names from tool definitions
                        action_names = []
                        for tool in service_tools:
                            if isinstance(tool, dict) and "function" in tool:
                                action_names.append(tool["function"]["name"])

                        # Get connection details
                        connection = await self.auth_service.get_connection_by_service(
                            user_id, service_name
                        )

                        tool_info = {
                            "service_name": service_name,
                            "app_enum": app_enum,
                            "available_actions": action_names,
                            "connection_id": (
                                connection.composio_connection_id
                                if connection
                                else None
                            ),
                            "tool_count": len(service_tools),
                        }

                        available_tools.append(tool_info)
                        logger.info(
                            f"✅ Found {len(action_names)} actions for {service_name}"
                        )

                except Exception as service_error:
                    logger.error(
                        f"Error fetching tools for {service_name}: {service_error}"
                    )
                    # Continue with other services
                    continue

            logger.info(
                f"Successfully fetched tools for {len(available_tools)} services for user {user_id}"
            )
            return available_tools

        except Exception as e:
            logger.error(f"Error fetching available tools for user {user_id}: {e}")
            return []

    def _get_app_enum(self, service_name: str) -> Optional[App]:
        """
        Map service name to Composio App enum.

        Following Composio's documented App enum patterns.

        Args:
            service_name: Service name from database (e.g., "gmail", "notion")

        Returns:
            Corresponding App enum or None if not found
        """
        service_mapping = {
            "gmail": App.GMAIL,
            "notion": App.NOTION,
            "github": App.GITHUB,
            "slack": App.SLACK,
            "google_drive": App.GOOGLEDRIVE,
            "googledrive": App.GOOGLEDRIVE,
            "google_calendar": App.GOOGLECALENDAR,
            "googlecalendar": App.GOOGLECALENDAR,
            "google_docs": App.GOOGLEDOCS,
            "googledocs": App.GOOGLEDOCS,
            "google_sheets": App.GOOGLESHEETS,
            "googlesheets": App.GOOGLESHEETS,
            "trello": App.TRELLO,
            "asana": App.ASANA,
            "linear": App.LINEAR,
            "jira": App.JIRA,
            "discord": App.DISCORD,
            "twitter": App.TWITTER,
            "linkedin": App.LINKEDIN,
            "hubspot": App.HUBSPOT,
            "salesforce": App.SALESFORCE,
            "zoom": App.ZOOM,
            "microsoft_teams": App.MICROSOFTTEAMS,
            "microsoftteams": App.MICROSOFTTEAMS,
            "dropbox": App.DROPBOX,
            "onedrive": App.ONEDRIVE,
            "box": App.BOX,
        }

        return service_mapping.get(service_name.lower())

    def _get_service_actions(self, service_name: str) -> List[str]:
        """
        Get comprehensive list of actions for a service.

        This method returns all the actions we want to support for each service,
        based on our parameter mappings and tool definitions.

        Args:
            service_name: Service name (e.g., "gmail", "notion")

        Returns:
            List of action names to request from Composio
        """
        # Comprehensive action lists based on our parameter mappings
        service_actions = {
            "gmail": [
                "GMAIL_SEND_EMAIL",
                "GMAIL_CREATE_EMAIL_DRAFT",
                "GMAIL_FETCH_EMAILS",
                "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID",
                "GMAIL_REPLY_TO_THREAD",
                "GMAIL_DELETE_MESSAGE",
                "GMAIL_MOVE_TO_TRASH",
                "GMAIL_LIST_DRAFTS",
                "GMAIL_DELETE_DRAFT",
                "GMAIL_ADD_LABEL_TO_EMAIL",
                "GMAIL_CREATE_LABEL",
                "GMAIL_LIST_LABELS",
                "GMAIL_REMOVE_LABEL",
                "GMAIL_GET_PROFILE",
                "GMAIL_GET_CONTACTS",
                "GMAIL_SEARCH_PEOPLE",
            ],
            "notion": [
                "NOTION_CREATE_NOTION_PAGE",
                "NOTION_SEARCH_NOTION_PAGE",
                "NOTION_FETCH_ROW",
                "NOTION_ADD_PAGE_CONTENT",
                "NOTION_APPEND_BLOCK_CHILDREN",
                "NOTION_CREATE_DATABASE",
                "NOTION_QUERY_DATABASE",
                "NOTION_INSERT_ROW_DATABASE",
                "NOTION_UPDATE_ROW_DATABASE",
                "NOTION_FETCH_DATABASE",
                "NOTION_ARCHIVE_NOTION_PAGE",
                "NOTION_DELETE_BLOCK",
                "NOTION_FETCH_NOTION_BLOCK",
                "NOTION_FETCH_NOTION_CHILD_BLOCK",
                "NOTION_CREATE_COMMENT",
                "NOTION_FETCH_COMMENTS",
                "NOTION_DUPLICATE_PAGE",
                "NOTION_NOTION_UPDATE_BLOCK",
                "NOTION_UPDATE_SCHEMA_DATABASE",
                "NOTION_GET_ABOUT_ME",
                "NOTION_GET_ABOUT_USER",
                "NOTION_LIST_USERS",
                "NOTION_GET_PAGE_PROPERTY_ACTION",
            ],
            "slack": [
                "SLACK_CHAT_POST_MESSAGE",
                "SLACK_ADD_REACTION_TO_AN_ITEM",
                "SLACK_CREATE_A_REMINDER",
                "SLACK_DELETES_A_MESSAGE_FROM_A_CHAT",
                "SLACK_CREATE_CHANNEL_BASED_CONVERSATION",
                "SLACK_ARCHIVE_A_SLACK_CONVERSATION",
                "SLACK_CONVERSATIONS_LIST",
                "SLACK_CONVERSATIONS_INFO",
                "SLACK_CONVERSATIONS_HISTORY",
            ],
            "googledrive": [
                "GOOGLEDRIVE_CREATE_FILE_FROM_TEXT",
                "GOOGLEDRIVE_CREATE_FOLDER",
                "GOOGLEDRIVE_FIND_FILE",
                "GOOGLEDRIVE_FIND_FOLDER",
                "GOOGLEDRIVE_DOWNLOAD_FILE",
                "GOOGLEDRIVE_EDIT_FILE",
                "GOOGLEDRIVE_COPY_FILE",
                "GOOGLEDRIVE_DELETE_FOLDER_OR_FILE",
                "GOOGLEDRIVE_UPLOAD_FILE",
                "GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE",
            ],
        }

        actions = service_actions.get(service_name.lower(), [])
        logger.debug(f"Found {len(actions)} predefined actions for {service_name}")
        return actions

    async def execute_xml_tool(
        self, user_id: str, service_name: str, action: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a Composio action via XML tool call with parameter mapping and response processing.

        This method follows Composio's documented execute_action pattern with
        user-specific entity isolation, enhanced with our processor system for
        bulletproof parameter mapping and response processing.

        Args:
            user_id: User's ID (used as entity_id)
            service_name: Name of the service (e.g., "gmail", "notion")
            action: Action name (e.g., "GMAIL_SEND_EMAIL")
            parameters: Action parameters (may use common names like "to", "subject")

        Returns:
            Processed execution result dictionary
        """
        logger.info(
            f"🚀 execute_xml_tool called: user={user_id}, service={service_name}, action={action}"
        )
        logger.debug(f"📝 Original parameters: {parameters}")

        try:
            # Step 1: Apply parameter mapping using our processor system
            param_mapper = create_parameter_mapper(service_name, action)
            mapped_parameters = param_mapper(parameters)

            logger.info(f"🔄 Applied parameter mapping for {service_name}.{action}")
            logger.debug(f"📝 Mapped parameters: {mapped_parameters}")

            # Log parameter mappings for debugging
            if mapped_parameters != parameters:
                logger.info(f"✨ Parameter mapping applied:")
                for orig_key, orig_value in parameters.items():
                    for mapped_key, mapped_value in mapped_parameters.items():
                        if orig_value == mapped_value and orig_key != mapped_key:
                            logger.info(f"  {orig_key} -> {mapped_key}")
                            break
            # Step 2: Get user's toolset
            logger.debug(f"🔍 Getting toolset for user {user_id}")
            user_toolset = await self._get_user_toolset(user_id)
            if not user_toolset:
                error_msg = f"No active toolset found for user {user_id}"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "service": service_name,
                    "action": action,
                }

            logger.debug(f"✅ Got toolset for user {user_id}")

            # Validate connection is still active
            logger.debug(f"🔍 Checking connection status for {service_name}")
            connection_status = await self.auth_service.check_connection_status(
                user_id, service_name
            )

            logger.debug(
                f"📊 Connection status: connected={connection_status.connected}, id={connection_status.connection_id}"
            )

            if not connection_status.connected:
                error_msg = f"Service {service_name} is not connected or inactive"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                }

            logger.info(
                f"🎯 Executing {action} for user {user_id} on service {service_name}"
            )

            # Validate action exists in Action enum
            try:
                action_enum = getattr(Action, action)
                logger.debug(f"✅ Action enum found: {action_enum}")
            except AttributeError:
                error_msg = f"Action '{action}' not found in Composio Action enum"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "action": action,
                    "service": service_name,
                }

            # Step 4: Execute using Composio's documented execute_action pattern with mapped parameters
            logger.debug(
                f"🔧 Calling user_toolset.execute_action with entity_id={user_id}"
            )
            raw_result = user_toolset.execute_action(
                action=action_enum,  # Convert string to Action enum
                params=mapped_parameters,  # Use mapped parameters instead of original
                entity_id=user_id,  # Explicit entity_id for user isolation
            )

            logger.debug(f"📤 Raw result from Composio: {raw_result}")

            # Step 5: Process the response using our processor system
            response_processor = create_response_processor(service_name, action)
            processed_result = response_processor(raw_result)

            logger.info(f"🔄 Applied response processing for {service_name}.{action}")
            logger.debug(f"📤 Processed result: {processed_result}")

            # Step 6: Update connection verification timestamp
            if connection_status.connection_id:
                logger.debug(
                    f"🔄 Updating connection verification for {connection_status.connection_id}"
                )
                await self.auth_service.update_connection_verification(
                    user_id, connection_status.connection_id
                )

            logger.info(
                f"🎉 Successfully executed and processed {action} for user {user_id}"
            )

            # Check for common errors and provide helpful guidance
            if not processed_result.get("success", True):
                processed_result = self._enhance_error_with_guidance(
                    service_name, action, processed_result, parameters
                )

            # Return the processed result (already has success, data, service, action fields)
            return processed_result

        except Exception as e:
            logger.error(
                f"💥 Error executing {action} for user {user_id}: {e}", exc_info=True
            )

            # Use response processor for consistent error formatting
            response_processor = create_response_processor(service_name, action)
            error_response = {
                "success": False,
                "error": str(e),
                "action": action,
                "service": service_name,
                "parameters": parameters,  # Include original parameters for debugging
            }

            return response_processor(error_response)

    def _enhance_error_with_guidance(
        self,
        service_name: str,
        action: str,
        result: Dict[str, Any],
        parameters: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Enhance error messages with helpful guidance for common issues.

        Args:
            service_name: Service name (e.g., "notion")
            action: Action name (e.g., "NOTION_CREATE_NOTION_PAGE")
            result: Original error result
            parameters: Parameters that were used

        Returns:
            Enhanced result with helpful guidance
        """
        try:
            error_msg = str(result.get("error", ""))

            # Notion-specific error guidance
            if service_name == "notion" and action == "NOTION_CREATE_NOTION_PAGE":
                if "parent_id" in error_msg.lower() and "missing" in error_msg.lower():
                    enhanced_error = (
                        f"❌ Notion Create Page Error: Missing required 'parent_id' parameter.\n\n"
                        f"💡 To fix this:\n"
                        f"1. First search for pages using: notion_execute action='search' query='your search'\n"
                        f"2. Find a suitable parent page ID from the search results\n"
                        f"3. Then create the page with: notion_execute action='create_page' title='your title' parent_id='found-page-id'\n\n"
                        f"📝 Note: Every Notion page needs a parent page. You can use your workspace root page as parent.\n\n"
                        f"Original error: {error_msg}"
                    )

                    result["error"] = enhanced_error
                    result["guidance"] = {
                        "action_needed": "search_for_parent",
                        "suggested_action": "notion_execute",
                        "suggested_parameters": {
                            "action": "search",
                            "query": "workspace OR root OR main",
                        },
                        "next_step": "Use the page ID from search results as parent_id",
                    }

            # Gmail-specific error guidance
            elif service_name == "gmail" and "recipient" in error_msg.lower():
                enhanced_error = (
                    f"❌ Gmail Error: {error_msg}\n\n"
                    f"💡 Make sure to provide recipient email address using 'to' parameter.\n"
                    f"Example: gmail_execute action='send_email' to='<EMAIL>' subject='Hello' body='Message'"
                )
                result["error"] = enhanced_error

            # Add more service-specific guidance as needed

            logger.debug(f"Enhanced error message for {service_name}.{action}")
            return result

        except Exception as e:
            logger.warning(f"Error enhancing error message: {e}")
            return result

    @staticmethod
    def from_env():
        """Create an instance from environment variables."""
        return ComposioXMLService(
            api_key=os.getenv("COMPOSIO_API_KEY"),
            supabase_url=os.getenv("SUPABASE_URL"),
            supabase_key=os.getenv("SUPABASE_SERVICE_ROLE_KEY"),
        )
